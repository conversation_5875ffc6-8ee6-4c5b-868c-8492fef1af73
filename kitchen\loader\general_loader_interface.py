from typing import Optional
import os.path as path
import os
import json

from kitchen.configs.routing import default_recipe_path
from kitchen.structure.hierarchical_data_structure import DataSet


def load_dataset(template_id: str, cohort_id: str, recipe: str, name: Optional[str] = None) -> DataSet:
    """
    Load a dataset for a given template and cohort using a specific recipe.
    """
    recipe_path = default_recipe_path(recipe)
    assert path.exists(recipe_path), f"Cannot find recipe path: {recipe_path}"
    with open(recipe_path, 'r') as f:
        recipe_dict = json.load(f)
    
        